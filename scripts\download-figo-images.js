const fs = require('fs')
const path = require('path')
const https = require('https')

// Create directories if they don't exist
const ensureDirectoryExists = (dirPath) => {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true })
  }
}

// Download function
const downloadImage = (url, filepath) => {
  return new Promise((resolve, reject) => {
    const file = fs.createWriteStream(filepath)
    
    https.get(url, (response) => {
      if (response.statusCode !== 200) {
        reject(new Error(`Failed to download ${url}: ${response.statusCode}`))
        return
      }
      
      response.pipe(file)
      
      file.on('finish', () => {
        file.close()
        console.log(`Downloaded: ${filepath}`)
        resolve()
      })
      
      file.on('error', (err) => {
        fs.unlink(filepath, () => {}) // Delete the file on error
        reject(err)
      })
    }).on('error', (err) => {
      reject(err)
    })
  })
}

// Images to download
const imagesToDownload = [
  {
    url: 'https://www.huisdierenverzekeringen.nl/build/images/hond.jpg',
    path: 'public/images/insurance-details/hond-tab.jpg'
  },
  {
    url: 'https://www.huisdierenverzekeringen.nl/build/images/kat.jpg',
    path: 'public/images/insurance-details/kat-tab.jpg'
  },
  {
    url: 'https://www.huisdierenverzekeringen.nl/build/images/konijn.jpg',
    path: 'public/images/insurance-details/konijn-tab.jpg'
  },
  {
    url: 'https://www.huisdierenverzekeringen.nl/build/images/papegaai.jpg',
    path: 'public/images/insurance-details/papegaai-tab.jpg'
  },
  {
    url: 'https://www.huisdierenverzekeringen.nl/uploads/images/hondenverzekering-2.jpg',
    path: 'public/images/figo/hondenverzekering-2.jpg'
  }
]

// Main download function
async function downloadAllImages() {
  try {
    // Ensure directories exist
    ensureDirectoryExists('public/images/insurance-details')
    ensureDirectoryExists('public/images/figo')
    
    console.log('Starting image downloads...')
    
    for (const image of imagesToDownload) {
      await downloadImage(image.url, image.path)
    }
    
    console.log('All images downloaded successfully!')
  } catch (error) {
    console.error('Error downloading images:', error)
    process.exit(1)
  }
}

downloadAllImages()
