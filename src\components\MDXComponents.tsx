import Image from 'next/image'
import Link from 'next/link'
import { createHeadingId } from '../lib/utils'
import { ReactNode } from 'react'

interface MDXComponentProps {
  children?: ReactNode
  [key: string]: unknown
}

// Custom components for MDX rendering
const MDXComponents = {
  // Headings with proper styling and anchor links
  h1: ({ children, ...props }: MDXComponentProps) => (
    <h1 className="text-4xl font-bold text-[#2F2E51] mb-6 leading-tight" {...props}>
      {children}
    </h1>
  ),
  h2: ({ children, ...props }: MDXComponentProps) => {
    const id = typeof children === 'string' ? createHeadingId(children) : undefined
    
    return (
      <h2 
        id={id}
        className="text-3xl font-bold text-[#2F2E51] mb-6 border-b-2 border-orange-200 pb-3 mt-12" 
        {...props}
      >
        {children}
      </h2>
    )
  },
  h3: ({ children, ...props }: MDXComponentProps) => {
    const id = typeof children === 'string' ? createHeadingId(children) : undefined
    
    return (
      <h3 
        id={id}
        className="text-xl font-semibold text-[#2F2E51] mb-4 mt-8" 
        {...props}
      >
        {children}
      </h3>
    )
  },
  h4: ({ children, ...props }: MDXComponentProps) => (
    <h4 className="text-lg font-semibold text-[#2F2E51] mb-3 mt-6" {...props}>
      {children}
    </h4>
  ),
  
  // Paragraphs
  p: ({ children, ...props }: MDXComponentProps) => (
    <p className="text-gray-700 leading-relaxed mb-6 text-lg" {...props}>
      {children}
    </p>
  ),
  
  // Lists
  ul: ({ children, ...props }: MDXComponentProps) => (
    <ul className="list-disc list-inside mb-6 space-y-2 text-gray-700" {...props}>
      {children}
    </ul>
  ),
  ol: ({ children, ...props }: MDXComponentProps) => (
    <ol className="list-decimal list-inside mb-6 space-y-2 text-gray-700" {...props}>
      {children}
    </ol>
  ),
  li: ({ children, ...props }: MDXComponentProps) => (
    <li className="text-gray-700 leading-relaxed" {...props}>
      {children}
    </li>
  ),
  
  // Links
  a: ({ href, children, ...props }: MDXComponentProps & { href?: string }) => {
    // Internal links
    if (href?.startsWith('/')) {
      return (
        <Link href={href} className="text-blue-600 hover:text-blue-800 underline transition-colors" {...props}>
          {children}
        </Link>
      )
    }
    
    // External links
    return (
      <a 
        href={href} 
        target="_blank" 
        rel="noopener noreferrer"
        className="text-blue-600 hover:text-blue-800 underline transition-colors" 
        {...props}
      >
        {children}
      </a>
    )
  },
  
  // Images
  img: ({ src, alt, ...props }: MDXComponentProps & { src?: string; alt?: string }) => (
    <Image
      src={src}
      alt={alt || ''}
      width={800}
      height={400}
      className="rounded-xl shadow-lg w-full h-auto my-8 block"
      {...props}
    />
  ),
  
  // Blockquotes
  blockquote: ({ children, ...props }: MDXComponentProps) => (
    <blockquote className="border-l-4 border-celeste-400 bg-celeste-900 p-6 my-8 italic" {...props}>
      {children}
    </blockquote>
  ),
  
  // Code blocks
  pre: ({ children, ...props }: MDXComponentProps) => (
    <pre className="bg-tea_green-800 rounded-lg p-4 overflow-x-auto mb-6 text-sm" {...props}>
      {children}
    </pre>
  ),
  code: ({ children, ...props }: MDXComponentProps) => (
    <code className="bg-tea_green-800 px-2 py-1 rounded text-sm font-mono" {...props}>
      {children}
    </code>
  ),
  
  // Tables
  table: ({ children, ...props }: MDXComponentProps) => (
    <div className="overflow-x-auto mb-8">
      <table className="min-w-full border border-gray-200 rounded-lg" {...props}>
        {children}
      </table>
    </div>
  ),
  th: ({ children, ...props }: MDXComponentProps) => (
    <th className="bg-gray-50 border border-gray-200 px-4 py-2 text-left font-semibold text-[#2F2E51]" {...props}>
      {children}
    </th>
  ),
  td: ({ children, ...props }: MDXComponentProps) => (
    <td className="border border-gray-200 px-4 py-2 text-gray-700" {...props}>
      {children}
    </td>
  ),
  
  // Horizontal rule
  hr: ({ ...props }: MDXComponentProps) => (
    <hr className="border-t-2 border-gray-200 my-12" {...props} />
  ),
  
  // Strong/Bold
  strong: ({ children, ...props }: MDXComponentProps) => (
    <strong className="font-semibold text-[#2F2E51]" {...props}>
      {children}
    </strong>
  ),
  
  // Emphasis/Italic
  em: ({ children, ...props }: MDXComponentProps) => (
    <em className="italic" {...props}>
      {children}
    </em>
  ),
}

export default MDXComponents
