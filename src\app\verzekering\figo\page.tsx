"use client"

import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import Image from 'next/image'
import Link from 'next/link'
import Navigation from '@/components/Navigation'

interface AnimalData {
  name: string
  image: string
  tabImage: string
  pricing: {
    percentage50: { price: string, cents: string }
    percentage70: { price: string, cents: string }
    percentage90: { price: string, cents: string }
  }
}

const animalData: Record<string, AnimalData> = {
  hond: {
    name: 'Hond',
    image: '/images/insurance-details/hond-tab.jpg',
    tabImage: '/images/insurance-details/hond-tab.jpg',
    pricing: {
      percentage50: { price: '17', cents: '25' },
      percentage70: { price: '28', cents: '47' },
      percentage90: { price: '41', cents: '92' }
    }
  },
  kat: {
    name: 'Kat',
    image: '/images/insurance-details/kat-tab.jpg',
    tabImage: '/images/insurance-details/kat-tab.jpg',
    pricing: {
      percentage50: { price: '11', cents: '41' },
      percentage70: { price: '17', cents: '88' },
      percentage90: { price: '31', cents: '19' }
    }
  },
  konijn: {
    name: 'Konijn',
    image: '/images/insurance-details/konijn-tab.jpg',
    tabImage: '/images/insurance-details/konijn-tab.jpg',
    pricing: {
      percentage50: { price: '12', cents: '83' },
      percentage70: { price: '18', cents: '78' },
      percentage90: { price: '27', cents: '09' }
    }
  },
  papegaai: {
    name: 'Papegaai',
    image: '/images/insurance-details/papegaai-tab.jpg',
    tabImage: '/images/insurance-details/papegaai-tab.jpg',
    pricing: {
      percentage50: { price: '19', cents: '28' },
      percentage70: { price: '27', cents: '45' },
      percentage90: { price: '34', cents: '79' }
    }
  }
}

export default function FigoInsurancePage() {
  const [selectedAnimal, setSelectedAnimal] = useState<string>('hond')
  const currentAnimal = animalData[selectedAnimal]

  const getFigoAffiliateLink = (animal: string) => {
    const baseUrl = 'https://www.awin1.com/cread.php?awinmid=24319&awinaffid=2107735&clickref=zoekdierenverzekering.nl&ued='
    const animalUrls = {
      hond: 'https%3A%2F%2Ffigopet.nl%2Fhondenverzekering%2F',
      kat: 'https%3A%2F%2Ffigopet.nl%2Fkattenverzekering%2F',
      konijn: 'https%3A%2F%2Ffigopet.nl%2Fkonijnenverzekering%2F',
      papegaai: 'https%3A%2F%2Ffigopet.nl%2Fpapegaaienverzekering%2F'
    }
    return baseUrl + (animalUrls[animal as keyof typeof animalUrls] || animalUrls.hond)
  }

  return (
    <div className="min-h-screen bg-white">
      <Navigation />
      
      {/* Hero Section */}
      <section className="pt-32 pb-16 bg-tea_green-900 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-tea_green-800/50 to-tea_green-900/80"></div>
        <div className="absolute top-0 right-0 w-96 h-96 bg-celadon-400/20 rounded-full -translate-y-48 translate-x-48"></div>
        
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* Left Content */}
            <div>
              <h1 className="text-5xl lg:text-6xl font-bold text-celadon-100 mb-6">
                Figo
              </h1>
              <div className="flex items-center gap-3 mb-8">
                <svg className="w-6 h-6 text-celadon-300" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
                <span className="text-xl text-celadon-100">Alle rassen en leeftijden welkom</span>
              </div>
            </div>

            {/* Right Content - Rating */}
            <div className="flex justify-center lg:justify-end">
              <div className="bg-white rounded-2xl p-6 shadow-xl border-2 border-celadon-300">
                <div className="text-center">
                  <div className="text-4xl font-bold text-tea_green-100 mb-2">7.9</div>
                  <button className="text-sm text-gray-600 hover:text-tea_green-100 transition-colors">
                    41 beoordelingen
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-4xl font-bold text-tea_green-100 text-center mb-12">
            Vergelijk alle specificaties
          </h2>

          {/* Animal Selection Tabs */}
          <div className="flex flex-wrap justify-center gap-4 mb-12">
            {Object.entries(animalData).map(([key, animal]) => (
              <button
                key={key}
                onClick={() => setSelectedAnimal(key)}
                className={`flex items-center rounded-lg border-2 overflow-hidden transition-all duration-300 ${
                  selectedAnimal === key
                    ? 'border-celadon-300 bg-tea_green-100 text-white shadow-lg'
                    : 'border-gray-300 bg-white text-tea_green-100 hover:border-celadon-200'
                }`}
              >
                <div className="w-20 h-16 relative">
                  <Image
                    src={animal.tabImage}
                    alt={animal.name}
                    fill
                    className="object-cover"
                  />
                </div>
                <div className="px-4 py-3">
                  <span className="font-semibold">{animal.name}</span>
                </div>
              </button>
            ))}
          </div>

          {/* Content Area with Fade Transition */}
          <AnimatePresence mode="wait">
            <motion.div
              key={selectedAnimal}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
              className="space-y-8"
            >
              {/* Pricing Cards - Figma Design */}
              <div className="grid md:grid-cols-3 gap-6">
                {/* 50% Coverage Card */}
                <div className="bg-white border border-gray-300 rounded-lg overflow-hidden shadow-sm">
                  {/* Header with Figo Logo */}
                  <div className="bg-white border-b border-gray-300 p-6 text-center">
                    <div className="mb-4">
                      <Image
                        src="/images/logos/figo-logo.svg"
                        alt="Figo"
                        width={168}
                        height={84}
                        className="mx-auto"
                      />
                    </div>
                  </div>

                  {/* Coverage Details */}
                  <div className="p-6 space-y-4">
                    <h3 className="text-2xl font-bold text-tea_green-100 mb-4">50%</h3>

                    <div className="space-y-3 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-700">Verzekerd bedrag</span>
                        <span className="font-bold text-gray-900">€ 3.000,00</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-700">Eigen risico</span>
                        <span className="font-bold text-gray-900">Geen</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-700">Eenmalige kosten</span>
                        <span className="font-bold text-gray-900">€ 0,00</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-700">Maximale leeftijd</span>
                        <span className="font-bold text-gray-900">N.v.t.</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-700">Wachttijd</span>
                        <span className="font-bold text-gray-900">30 dagen</span>
                      </div>
                    </div>
                  </div>

                  {/* Pricing Section */}
                  <div className="bg-orange-50 p-6 text-center">
                    <div className="mb-4">
                      <span className="text-gray-700 text-sm">vanaf </span>
                      <span className="text-3xl font-black text-gray-900">€ {currentAnimal.pricing.percentage50.price},</span>
                      <span className="text-xl font-black text-gray-900">{currentAnimal.pricing.percentage50.cents}</span>
                      <span className="text-gray-700 text-sm"> p/m</span>
                    </div>

                    <a
                      href={getFigoAffiliateLink(selectedAnimal)}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="block bg-tea_green-100 text-white px-8 py-3 rounded-full font-bold hover:bg-tea_green-200 transition-colors mb-4"
                    >
                      Bekijken &gt;
                    </a>

                    <button className="text-gray-700 text-sm hover:underline">
                      Bekijk alle details
                    </button>
                  </div>
                </div>

                {/* 70% Coverage Card */}
                <div className="bg-white border border-gray-300 rounded-lg overflow-hidden shadow-sm">
                  {/* Header with Figo Logo */}
                  <div className="bg-white border-b border-gray-300 p-6 text-center">
                    <div className="mb-4">
                      <Image
                        src="/images/logos/figo-logo.svg"
                        alt="Figo"
                        width={168}
                        height={84}
                        className="mx-auto"
                      />
                    </div>
                  </div>

                  {/* Coverage Details */}
                  <div className="p-6 space-y-4">
                    <h3 className="text-2xl font-bold text-tea_green-100 mb-4">70%</h3>

                    <div className="space-y-3 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-700">Verzekerd bedrag</span>
                        <span className="font-bold text-gray-900">€ 3.000,00</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-700">Eigen risico</span>
                        <span className="font-bold text-gray-900">Geen</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-700">Eenmalige kosten</span>
                        <span className="font-bold text-gray-900">€ 0,00</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-700">Maximale leeftijd</span>
                        <span className="font-bold text-gray-900">N.v.t.</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-700">Wachttijd</span>
                        <span className="font-bold text-gray-900">30 dagen</span>
                      </div>
                    </div>
                  </div>

                  {/* Pricing Section */}
                  <div className="bg-orange-50 p-6 text-center">
                    <div className="mb-4">
                      <span className="text-gray-700 text-sm">vanaf </span>
                      <span className="text-3xl font-black text-gray-900">€ {currentAnimal.pricing.percentage70.price},</span>
                      <span className="text-xl font-black text-gray-900">{currentAnimal.pricing.percentage70.cents}</span>
                      <span className="text-gray-700 text-sm"> p/m</span>
                    </div>

                    <a
                      href={getFigoAffiliateLink(selectedAnimal)}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="block bg-tea_green-100 text-white px-8 py-3 rounded-full font-bold hover:bg-tea_green-200 transition-colors mb-4"
                    >
                      Bekijken &gt;
                    </a>

                    <button className="text-gray-700 text-sm hover:underline">
                      Bekijk alle details
                    </button>
                  </div>
                </div>

                {/* 90% Coverage Card */}
                <div className="bg-white border border-gray-300 rounded-lg overflow-hidden shadow-sm">
                  {/* Header with Figo Logo */}
                  <div className="bg-white border-b border-gray-300 p-6 text-center">
                    <div className="mb-4">
                      <Image
                        src="/images/logos/figo-logo.svg"
                        alt="Figo"
                        width={168}
                        height={84}
                        className="mx-auto"
                      />
                    </div>
                  </div>

                  {/* Coverage Details */}
                  <div className="p-6 space-y-4">
                    <h3 className="text-2xl font-bold text-tea_green-100 mb-4">90%</h3>

                    <div className="space-y-3 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-700">Verzekerd bedrag</span>
                        <span className="font-bold text-gray-900">€ 3.000,00</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-700">Eigen risico</span>
                        <span className="font-bold text-gray-900">Geen</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-700">Eenmalige kosten</span>
                        <span className="font-bold text-gray-900">€ 0,00</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-700">Maximale leeftijd</span>
                        <span className="font-bold text-gray-900">N.v.t.</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-700">Wachttijd</span>
                        <span className="font-bold text-gray-900">30 dagen</span>
                      </div>
                    </div>
                  </div>

                  {/* Pricing Section */}
                  <div className="bg-orange-50 p-6 text-center">
                    <div className="mb-4">
                      <span className="text-gray-700 text-sm">vanaf </span>
                      <span className="text-3xl font-black text-gray-900">€ {currentAnimal.pricing.percentage90.price},</span>
                      <span className="text-xl font-black text-gray-900">{currentAnimal.pricing.percentage90.cents}</span>
                      <span className="text-gray-700 text-sm"> p/m</span>
                    </div>

                    <a
                      href={getFigoAffiliateLink(selectedAnimal)}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="block bg-tea_green-100 text-white px-8 py-3 rounded-full font-bold hover:bg-tea_green-200 transition-colors mb-4"
                    >
                      Bekijken &gt;
                    </a>

                    <button className="text-gray-700 text-sm hover:underline">
                      Bekijk alle details
                    </button>
                  </div>
                </div>
              </div>

              {/* Compare Button */}
              <div className="text-center">
                <Link
                  href="/"
                  className="inline-block bg-white border-2 border-tea_green-100 text-tea_green-100 px-8 py-3 rounded-full font-bold hover:bg-tea_green-100 hover:text-white transition-colors"
                >
                  Vergelijk huisdierverzekeringen
                </Link>
              </div>
            </motion.div>
          </AnimatePresence>
        </div>
      </section>

      {/* Why Choose Figo Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <Image
                src="/images/figo/hondenverzekering-2.jpg"
                alt="Hondenverzekering"
                width={600}
                height={400}
                className="rounded-lg shadow-lg"
              />
            </div>
            <div>
              <h2 className="text-3xl font-bold text-tea_green-900 mb-6">
                Waarom kiezen voor een dierenverzekering van Figo?
              </h2>
              <p className="text-gray-700 mb-6">
                Figo Pet helpt meer dan 1,5 miljoen huisdieren wereldwijd. Daardoor wordt Figo gezien als dé specialist in huisdierenverzekeringen. Als enige aanbieder in Nederland verzekert Figo niet alleen honden en katten, maar ook papegaaien en konijnen. Bovendien kun je zowel de dekking, als het verzekerde bedrag van de dierenverzekering van Figo zelf kiezen. Optimale flexibiliteit dus!
              </p>
              <div className="space-y-4">
                <h3 className="text-xl font-semibold text-tea_green-900">Alle voordelen op een rij</h3>
                <ul className="space-y-2">
                  <li className="flex items-center">
                    <svg className="w-5 h-5 text-celadon-300 mr-3" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    <span className="text-gray-700">Van dierenliefhebbers, voor dierenliefhebbers</span>
                  </li>
                  <li className="flex items-center">
                    <svg className="w-5 h-5 text-celadon-300 mr-3" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    <span className="text-gray-700">Zéér uitgebreide basisdekking</span>
                  </li>
                  <li className="flex items-center">
                    <svg className="w-5 h-5 text-celadon-300 mr-3" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    <span className="text-gray-700">Alle rassen en dieren van alle leeftijden zijn welkom</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* About Figo Section */}
      <section className="py-16">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-tea_green-900 mb-8 text-center">
            Alles wat je moet weten over Figo
          </h2>
          <div className="prose prose-lg max-w-none text-gray-700">
            <p>
              Figo is onderdeel van een grotere organisatie, waarvan het hoofdkantoor in het Luxemburg gevestigd is. Onder de naam Figo is het bedrijf ook in onder meer België en de Verenigde Staten actief. Op het Nederlandse kantoor van Figo werken veel échte specialisten. Zo hebben alle medewerkers van de klantenservice een diergeneeskundige achtergrond.
            </p>
            <p>
              De missie van Figo is duidelijk: zij willen de beste zorg voor jouw huisdier. Daarbij zie je de liefde voor dieren terug in alle diensten, medewerkers en in de klantenservice. Figo werkt intensief samen met dierenartsen en fokkers om zo goede en complete zorgverzekeringen aan te kunnen bieden. Bij Figo kun je kiezen of je 50%, 70% of 90% van je dierenartsrekening vergoed wilt krijgen. Ook kun je de maximale vergoeding per verzekeringsjaar (€ 3.000, € 6.000 of onbeperkt) zelf selecteren. Voor een hogere dekking en maximale vergoeding betaal je dan ook een hogere maandpremie.
            </p>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-tea_green-900 mb-8 text-center">
            Vraag en antwoord
          </h2>
          <p className="text-gray-700 mb-8 text-center">
            Wij geven antwoord op veelgestelde vragen over de dierenverzekeringen van Figo:
          </p>

          <div className="space-y-6">
            <details className="bg-white rounded-lg shadow-sm border border-gray-200">
              <summary className="p-6 cursor-pointer font-semibold text-tea_green-900 hover:bg-gray-50">
                Wat kost een huisdierenverzekering van Figo?
              </summary>
              <div className="px-6 pb-6 text-gray-700">
                <p>De dierenverzekering van Figo kun je naar eigen wens aanpassen. De basisverzekering kun je afsluiten vanaf ongeveer € 11 per maand voor een kat of € 17 voor een hond. Voor de meest uitgebreide totaalverzekering ben je maandelijks zo'n € 30 voor een kat of € 40 voor een hond kwijt. Bij grotere rassen, of rassen die vaak gezondheidsproblemen hebben, kan de maandpremie hoger uitvallen.</p>
              </div>
            </details>

            <details className="bg-white rounded-lg shadow-sm border border-gray-200">
              <summary className="p-6 cursor-pointer font-semibold text-tea_green-900 hover:bg-gray-50">
                Welke dieren kun je bij Figo verzekeren?
              </summary>
              <div className="px-6 pb-6 text-gray-700">
                <p>Figo Pet is momenteel de enige huisdierenverzekeraar in Nederland waar je, naast katten en honden, ook een konijn of papegaai kunt verzekeren.</p>
              </div>
            </details>

            <details className="bg-white rounded-lg shadow-sm border border-gray-200">
              <summary className="p-6 cursor-pointer font-semibold text-tea_green-900 hover:bg-gray-50">
                Heeft Figo ook aanvullende verzekeringen?
              </summary>
              <div className="px-6 pb-6 text-gray-700">
                <p>Bij Figo kun je er voor kiezen om de basisdekking uit te breiden met twee verschillende aanvullende verzekeringen. Voor een kleine meerprijs kun je er voor zorgen dat jouw huisdier ook verzekerd is voor Gebitsbehandelingen of Ziektekosten op reis.</p>
              </div>
            </details>

            <details className="bg-white rounded-lg shadow-sm border border-gray-200">
              <summary className="p-6 cursor-pointer font-semibold text-tea_green-900 hover:bg-gray-50">
                Krijg je korting als je meerdere dieren bij Figo verzekert?
              </summary>
              <div className="px-6 pb-6 text-gray-700">
                <p>Sluit je voor meerdere huisdieren een verzekering af bij Figo? Vanaf het tweede dier ontvang je dan 5% korting op de premie voor al je verzekerde huisdieren.</p>
              </div>
            </details>

            <details className="bg-white rounded-lg shadow-sm border border-gray-200">
              <summary className="p-6 cursor-pointer font-semibold text-tea_green-900 hover:bg-gray-50">
                Kun je bij Figo ook oudere dieren verzekeren?
              </summary>
              <div className="px-6 pb-6 text-gray-700">
                <p>In tegenstelling tot bij andere verzekeraars, kun je bij Figo ook een verzekering afsluiten voor oudere huisdieren. Figo hanteert als enige verzekeraar géén leeftijdslimiet voor katten of honden.</p>
              </div>
            </details>

            <details className="bg-white rounded-lg shadow-sm border border-gray-200">
              <summary className="p-6 cursor-pointer font-semibold text-tea_green-900 hover:bg-gray-50">
                Kun je bij Figo ook andere knaagdieren verzekeren?
              </summary>
              <div className="px-6 pb-6 text-gray-700">
                <p>Een konijnenverzekering kun je uiteraard afsluiten voor alle tamme konijnensoorten, maar ook voor een chinchilla. Naast chinchilla's kun je echter geen andere knaagdieren (zoals een cavia of een hamster) verzekeren.</p>
              </div>
            </details>

            <details className="bg-white rounded-lg shadow-sm border border-gray-200">
              <summary className="p-6 cursor-pointer font-semibold text-tea_green-900 hover:bg-gray-50">
                Welke vogelsoorten kun je verzekeren bij Figo?
              </summary>
              <div className="px-6 pb-6 text-gray-700">
                <p>Met de papegaaienverzekering van Figo kun je onder meer een agapornis, ara, grijze roodstaart, kaketoe of (halsband)parkiet verzekeren. Ook enkele andere amazonevogels vallen onder de dekking van een papegaaienverzekering. Onder meer kippen, kanaries of duiven kun je niet verzekeren.</p>
              </div>
            </details>
          </div>
        </div>
      </section>

      {/* Reviews Section */}
      <section className="py-16">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold text-tea_green-900 mb-8">
            Beoordelingen van Figo
          </h2>

          <div className="bg-white rounded-lg shadow-lg p-8 mb-8">
            <div className="text-6xl font-bold text-tea_green-900 mb-4">7.9</div>
            <p className="text-gray-600 mb-6">Op basis van 41 klantbeoordelingen</p>

            <div className="grid md:grid-cols-4 gap-6">
              <div>
                <div className="text-2xl font-bold text-tea_green-900">8.3</div>
                <div className="text-sm text-gray-600">Gemak van afsluiten</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-tea_green-900">8.1</div>
                <div className="text-sm text-gray-600">Klantenservice</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-tea_green-900">7.9</div>
                <div className="text-sm text-gray-600">Declaraties</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-tea_green-900">7.2</div>
                <div className="text-sm text-gray-600">Prijs-kwaliteit</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Back to Comparison */}
      <section className="py-16 bg-tea_green-900">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold text-celadon-100 mb-6">
            Wil je meer verzekeraars vergelijken?
          </h2>
          <p className="text-xl text-celadon-200 mb-8">
            Ga terug naar onze vergelijker om alle Nederlandse dierenverzekeraars te bekijken.
          </p>
          <Link
            href="/"
            className="inline-block bg-celadon-300 text-white px-8 py-4 rounded-full font-semibold hover:bg-celadon-400 transition-colors"
          >
            Terug naar vergelijker
          </Link>
        </div>
      </section>
    </div>
  )
}
