/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './pages/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',
    './app/**/*.{js,ts,jsx,tsx,mdx}',
    './src/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      fontFamily: {
        sans: ['var(--font-poppins)', 'system-ui', 'sans-serif'],
        figtree: ['var(--font-figtree)', 'system-ui', 'sans-serif'],
      },
      colors: {
        'celeste': {
          DEFAULT: '#bcf4f5',
          100: '#0b4a4b',
          200: '#179597',
          300: '#26dbdf',
          400: '#71e8ea',
          500: '#bcf4f5',
          600: '#caf6f7',
          700: '#d7f8f9',
          800: '#e4fbfb',
          900: '#f2fdfd'
        },
        'celadon': {
          DEFAULT: '#b4ebca',
          100: '#114125',
          200: '#238349',
          300: '#34c46e',
          400: '#71d99b',
          500: '#b4ebca',
          600: '#c2efd4',
          700: '#d1f3df',
          800: '#e0f7e9',
          900: '#f0fbf4'
        },
        'tea_green': {
          DEFAULT: '#d9f2b4',
          100: '#30480d',
          200: '#619019',
          300: '#91d826',
          400: '#b5e56d',
          500: '#d9f2b4',
          600: '#e1f5c4',
          700: '#e9f7d3',
          800: '#f0fae2',
          900: '#f8fcf0'
        },
        'tea_green_alt': {
          DEFAULT: '#d3fac7',
          100: '#195307',
          200: '#32a50e',
          300: '#51ec22',
          400: '#92f374',
          500: '#d3fac7',
          600: '#dcfbd2',
          700: '#e4fcdd',
          800: '#edfde8',
          900: '#f6fef4'
        },
        'cherry_blossom_pink': {
          DEFAULT: '#ffb7c3',
          100: '#58000f',
          200: '#af001d',
          300: '#ff0831',
          400: '#ff607a',
          500: '#ffb7c3',
          600: '#ffc6cf',
          700: '#ffd4db',
          800: '#ffe2e7',
          900: '#fff1f3'
        }
      },
      backgroundImage: {
        'gradient-radial': 'radial-gradient(var(--tw-gradient-stops))',
        'gradient-diamond': 'radial-gradient(ellipse at 50% 100%, var(--tw-gradient-stops))',
      },
    },
  },
  plugins: [],
}