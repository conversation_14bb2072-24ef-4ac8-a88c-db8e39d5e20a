"use client"

import { motion, AnimatePresence } from "framer-motion";
import { useState } from "react";

interface Review {
  id: string;
  name: string;
  rating: number;
  comment: string;
  date: string;
  categories: {
    gemakVanAfsluiten: number;
    klantenservice: number;
    declaraties: number;
    prijsKwaliteit: number;
  };
}

interface ReviewsPopupProps {
  isOpen: boolean;
  onClose: () => void;
  companyName: string;
  overallRating: number;
  totalReviews: number;
}

const figoReviews: Review[] = [
  {
    id: "1",
    name: "<PERSON><PERSON><PERSON>",
    rating: 9,
    comment: "Prima verzekering declaraties zijn makkelijk in te dienen en betaling is ook binnen een redelijk termijn.",
    date: "2 weken geleden",
    categories: {
      gemakVanAfsluiten: 9,
      klantenservice: 9,
      declaraties: 9,
      prijsKwaliteit: 9
    }
  },
  {
    id: "2",
    name: "<PERSON><PERSON>",
    rating: 8,
    comment: "Goed. Alleen jammer dat ze geen preventieve screeningen vergoeden.",
    date: "1 maand geleden",
    categories: {
      gemakVanAfsluiten: 8,
      klantenservice: 8,
      declaraties: 8,
      prijsKwaliteit: 8
    }
  },
  {
    id: "3",
    name: "Marco Ziepzeerder",
    rating: 5,
    comment: "Dierenverzekering werkt prima, alleen de premie is wel aan de hoge kant voor de dekking die je krijgt.",
    date: "3 weken geleden",
    categories: {
      gemakVanAfsluiten: 4,
      klantenservice: 6,
      declaraties: 5,
      prijsKwaliteit: 4
    }
  },
  {
    id: "4",
    name: "Sandra van der Berg",
    rating: 9,
    comment: "Uitstekende schadeafhandeling en snelle vergoeding van dierenarts kosten. Zeer tevreden met de dekking.",
    date: "1 week geleden",
    categories: {
      gemakVanAfsluiten: 9,
      klantenservice: 9,
      declaraties: 10,
      prijsKwaliteit: 8
    }
  },
  {
    id: "5",
    name: "Peter Jansen",
    rating: 7,
    comment: "Goede dierenverzekering met redelijke premie. Declaraties worden snel afgehandeld door de verzekeraar.",
    date: "2 maanden geleden",
    categories: {
      gemakVanAfsluiten: 7,
      klantenservice: 7,
      declaraties: 8,
      prijsKwaliteit: 7
    }
  }
];

const ohraReviews: Review[] = [
  {
    id: "1",
    name: "Maria Hendriksen",
    rating: 9,
    comment: "Zeer tevreden met OHRA dierenverzekering. Snelle schadeafhandeling en goede dekking voor mijn hond.",
    date: "1 week geleden",
    categories: {
      gemakVanAfsluiten: 9,
      klantenservice: 9,
      declaraties: 9,
      prijsKwaliteit: 8
    }
  },
  {
    id: "2",
    name: "Jan de Vries",
    rating: 8,
    comment: "Prima verzekering voor huisdieren. De premie is redelijk en vergoeding van dierenarts kosten gaat vlot.",
    date: "3 weken geleden",
    categories: {
      gemakVanAfsluiten: 8,
      klantenservice: 8,
      declaraties: 9,
      prijsKwaliteit: 8
    }
  },
  {
    id: "3",
    name: "Linda Bakker",
    rating: 10,
    comment: "Perfecte dierenverzekering! Uitstekende klantenservice en zeer snelle declaratie afhandeling.",
    date: "2 dagen geleden",
    categories: {
      gemakVanAfsluiten: 10,
      klantenservice: 10,
      declaraties: 10,
      prijsKwaliteit: 9
    }
  },
  {
    id: "4",
    name: "Robert Smit",
    rating: 7,
    comment: "Goede verzekering met redelijke dekking. Alleen de premie zou wat lager mogen voor de geboden service.",
    date: "1 maand geleden",
    categories: {
      gemakVanAfsluiten: 7,
      klantenservice: 8,
      declaraties: 7,
      prijsKwaliteit: 6
    }
  },
  {
    id: "5",
    name: "Inge Willems",
    rating: 9,
    comment: "Zeer goede ervaring met OHRA. Snelle vergoeding en uitstekende communicatie over de dekking.",
    date: "2 weken geleden",
    categories: {
      gemakVanAfsluiten: 9,
      klantenservice: 9,
      declaraties: 9,
      prijsKwaliteit: 8
    }
  },
  {
    id: "6",
    name: "Tom van Leeuwen",
    rating: 8,
    comment: "Goede dierenverzekering met transparante voorwaarden. Declaraties worden snel en correct afgehandeld.",
    date: "5 dagen geleden",
    categories: {
      gemakVanAfsluiten: 8,
      klantenservice: 8,
      declaraties: 9,
      prijsKwaliteit: 7
    }
  }
];

export default function ReviewsPopup({ isOpen, onClose, companyName, overallRating, totalReviews }: ReviewsPopupProps) {
  const [activeTab, setActiveTab] = useState<'reviews' | 'write'>('reviews');
  const [showSuccessMessage, setShowSuccessMessage] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    overallRating: 0,
    gemakVanAfsluiten: 0,
    klantenservice: 0,
    declaraties: 0,
    prijsKwaliteit: 0,
    comment: ''
  });

  const reviews = companyName === 'Figo' ? figoReviews : ohraReviews;
  
  const averageCategories = {
    gemakVanAfsluiten: Math.round(reviews.reduce((sum, review) => sum + review.categories.gemakVanAfsluiten, 0) / reviews.length * 10) / 10,
    klantenservice: Math.round(reviews.reduce((sum, review) => sum + review.categories.klantenservice, 0) / reviews.length * 10) / 10,
    declaraties: Math.round(reviews.reduce((sum, review) => sum + review.categories.declaraties, 0) / reviews.length * 10) / 10,
    prijsKwaliteit: Math.round(reviews.reduce((sum, review) => sum + review.categories.prijsKwaliteit, 0) / reviews.length * 10) / 10
  };

  const handleFormSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Simulate form submission
    setShowSuccessMessage(true);
    setTimeout(() => {
      setShowSuccessMessage(false);
      setActiveTab('reviews');
      setFormData({
        name: '',
        email: '',
        overallRating: 0,
        gemakVanAfsluiten: 0,
        klantenservice: 0,
        declaraties: 0,
        prijsKwaliteit: 0,
        comment: ''
      });
    }, 6000);
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <span key={i} className={`text-lg ${i < rating ? 'text-green-500' : 'text-gray-300'}`}>
        ★
      </span>
    ));
  };

  const renderRatingInput = (label: string, value: number, onChange: (rating: number) => void) => {
    return (
      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-700 mb-2">{label}</label>
        <div className="flex gap-1">
          {Array.from({ length: 10 }, (_, i) => (
            <button
              key={i}
              type="button"
              onClick={() => onChange(i + 1)}
              className={`w-8 h-8 rounded-full text-sm font-bold transition-colors ${
                i < value
                  ? 'bg-green-500 text-white'
                  : 'bg-gray-200 text-gray-600 hover:bg-gray-300'
              }`}
            >
              {i + 1}
            </button>
          ))}
        </div>
      </div>
    );
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 z-50"
            onClick={onClose}
          />
          
          {/* Modal */}
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: 20 }}
            transition={{ duration: 0.2 }}
            className="fixed inset-0 z-50 flex items-center justify-center p-4"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="bg-white rounded-2xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
              {/* Header */}
              <div className="bg-[#2F2E51] text-white p-6 relative">
                <button
                  onClick={onClose}
                  className="absolute top-4 right-4 text-white hover:text-gray-300 transition-colors"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
                
                <h2 className="text-2xl font-bold mb-2">Beoordelingen van {companyName}</h2>
                
                {/* Overall Rating */}
                <div className="bg-[#2F2E51] rounded-lg p-4 mb-4">
                  <div className="flex items-center justify-between text-white">
                    <div className="flex items-center">
                      <span className="text-4xl font-bold text-green-400 mr-4">{overallRating}</span>
                      <div>
                        <div className="text-sm opacity-90">Op basis van {totalReviews}</div>
                        <div className="text-sm opacity-90">klantbeoordelingen</div>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div className="flex justify-between">
                        <span>Gemak van afsluiten</span>
                        <span className="font-bold">{averageCategories.gemakVanAfsluiten}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Declaraties</span>
                        <span className="font-bold">{averageCategories.declaraties}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Klantenservice</span>
                        <span className="font-bold">{averageCategories.klantenservice}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Prijs-kwaliteit</span>
                        <span className="font-bold">{averageCategories.prijsKwaliteit}</span>
                      </div>
                    </div>
                  </div>
                </div>
                
                {/* Tab Navigation */}
                <div className="flex justify-center gap-4">
                  <button
                    onClick={() => setActiveTab('reviews')}
                    className={`px-6 py-2 rounded-full font-semibold transition-colors ${
                      activeTab === 'reviews'
                        ? 'bg-white text-[#2F2E51]'
                        : 'bg-transparent text-white border border-white hover:bg-white hover:text-[#2F2E51]'
                    }`}
                  >
                    Beoordelingen
                  </button>
                  <button
                    onClick={() => setActiveTab('write')}
                    className={`px-6 py-2 rounded-full font-semibold transition-colors ${
                      activeTab === 'write'
                        ? 'bg-white text-[#2F2E51]'
                        : 'bg-transparent text-white border border-white hover:bg-white hover:text-[#2F2E51]'
                    }`}
                  >
                    Schrijf een beoordeling
                  </button>
                </div>
              </div>
              
              {/* Content Area */}
              <div className="p-6 max-h-[60vh] overflow-y-auto">
                {activeTab === 'reviews' ? (
                  /* Reviews List */
                  <div className="space-y-6">
                    {reviews.map((review) => (
                      <div key={review.id} className="border-b border-gray-200 pb-6 last:border-b-0">
                        <div className="flex justify-between items-start mb-3">
                          <div>
                            <h4 className="font-semibold text-[#2F2E51]">{review.name}</h4>
                            <div className="flex items-center gap-2">
                              {renderStars(review.rating)}
                              <span className="font-bold text-lg">{review.rating}.0</span>
                            </div>
                          </div>

                          <div className="text-right text-sm space-y-1">
                            <div className="grid grid-cols-2 gap-2 text-xs">
                              <div className="flex justify-between">
                                <span>Gemak van afsluiten</span>
                                <span className="font-bold">{review.categories.gemakVanAfsluiten}</span>
                              </div>
                              <div className="flex justify-between">
                                <span>Declaraties</span>
                                <span className="font-bold">{review.categories.declaraties}</span>
                              </div>
                              <div className="flex justify-between">
                                <span>Klantenservice</span>
                                <span className="font-bold">{review.categories.klantenservice}</span>
                              </div>
                              <div className="flex justify-between">
                                <span>Prijs-kwaliteit</span>
                                <span className="font-bold">{review.categories.prijsKwaliteit}</span>
                              </div>
                            </div>
                          </div>
                        </div>

                        <p className="text-gray-700 mb-2">{review.comment}</p>
                        <p className="text-sm text-gray-500">{review.date}</p>
                      </div>
                    ))}
                  </div>
                ) : (
                  /* Write Review Form */
                  <div>
                    {showSuccessMessage ? (
                      <div className="text-center py-8">
                        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                          <strong>Bedankt!</strong> Uw beoordeling is succesvol verzonden.
                        </div>
                        <p className="text-gray-600">Uw beoordeling wordt binnen 24 uur gecontroleerd en gepubliceerd.</p>
                      </div>
                    ) : (
                      <form onSubmit={handleFormSubmit} className="space-y-6">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                              Naam *
                            </label>
                            <input
                              type="text"
                              required
                              value={formData.name}
                              onChange={(e) => setFormData({...formData, name: e.target.value})}
                              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#4DC166] text-gray-900 bg-white"
                              placeholder="Uw naam"
                            />
                          </div>
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                              E-mailadres *
                            </label>
                            <input
                              type="email"
                              required
                              value={formData.email}
                              onChange={(e) => setFormData({...formData, email: e.target.value})}
                              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#4DC166] text-gray-900 bg-white"
                              placeholder="<EMAIL>"
                            />
                          </div>
                        </div>

                        {renderRatingInput(
                          "Algemene beoordeling *",
                          formData.overallRating,
                          (rating) => setFormData({...formData, overallRating: rating})
                        )}

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          {renderRatingInput(
                            "Gemak van afsluiten",
                            formData.gemakVanAfsluiten,
                            (rating) => setFormData({...formData, gemakVanAfsluiten: rating})
                          )}
                          {renderRatingInput(
                            "Klantenservice",
                            formData.klantenservice,
                            (rating) => setFormData({...formData, klantenservice: rating})
                          )}
                          {renderRatingInput(
                            "Declaraties",
                            formData.declaraties,
                            (rating) => setFormData({...formData, declaraties: rating})
                          )}
                          {renderRatingInput(
                            "Prijs/Kwaliteit verhouding",
                            formData.prijsKwaliteit,
                            (rating) => setFormData({...formData, prijsKwaliteit: rating})
                          )}
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Uw ervaring *
                          </label>
                          <textarea
                            required
                            rows={4}
                            value={formData.comment}
                            onChange={(e) => setFormData({...formData, comment: e.target.value})}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#4DC166] text-gray-900 bg-white"
                            placeholder="Vertel over uw ervaring met deze verzekeraar..."
                          />
                        </div>

                        <div className="text-center">
                          <button
                            type="submit"
                            className="bg-[#4DC166] text-white px-8 py-3 rounded-full font-semibold hover:bg-[#45b05c] transition-colors"
                          >
                            Verstuur beoordeling
                          </button>
                        </div>
                      </form>
                    )}
                  </div>
                )}
              </div>
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
}
