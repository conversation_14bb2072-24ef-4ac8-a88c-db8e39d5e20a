// Script to systematically scrape all info content from huisdierenverzekeringen.nl
// This will be used as a reference for the manual scraping process

const urls = [
  'https://www.huisdierenverzekeringen.nl/info',
  'https://www.huisdierenverzekeringen.nl/info/konijn-benodigdheden',
  'https://www.huisdierenverzekeringen.nl/info/hondenbelasting',
  'https://www.huisdierenverzekeringen.nl/info/hondenverzekering-leeftijd',
  'https://www.huisdierenverzekeringen.nl/info/onderzoek-kosten-dierenarts',
  'https://www.huisdierenverzekeringen.nl/info/dekking-gedragstherapie',
  'https://www.huisdierenverzekeringen.nl/info/dierenverzekering-ontvlooien-antiteek',
  'https://www.huisdierenverzekeringen.nl/info/hond-kat-buitenland',
  'https://www.huisdierenverzekeringen.nl/info/hond-verzekeren-verplicht',
  'https://www.huisdierenverzekeringen.nl/info/premie-dierenverzekering-gestegen',
  'https://www.huisdierenverzekeringen.nl/info/konijn-verzekeren-verstandig',
  'https://www.huisdierenverzekeringen.nl/info/wat-vergoedt-of-dekt-een-dierenverzekering',
  'https://www.huisdierenverzekeringen.nl/info/overgewicht-hond-kat',
  'https://www.huisdierenverzekeringen.nl/info/hondenverzekering-elk-ras',
  'https://www.huisdierenverzekeringen.nl/info/chocolade-huisdieren',
  'https://www.huisdierenverzekeringen.nl/info/kattenverzekering-leeftijd',
  'https://www.huisdierenverzekeringen.nl/info/dekking-dieetvoeding-supplementen',
  'https://www.huisdierenverzekeringen.nl/info/dierenarts-kiezen',
  'https://www.huisdierenverzekeringen.nl/info/onderzoek-kosten-dierenarts-2024',
  'https://www.huisdierenverzekeringen.nl/info/welke-hond-past-bij-mij',
  'https://www.huisdierenverzekeringen.nl/info/wat-kost-een-kattenverzekering',
  'https://www.huisdierenverzekeringen.nl/info/kat-benodigdheden',
  'https://www.huisdierenverzekeringen.nl/info/ziek-huisdier-verzekeren',
  'https://www.huisdierenverzekeringen.nl/info/eigen-risico-eigen-bijdrage',
  'https://www.huisdierenverzekeringen.nl/info/chippen-hond',
  'https://www.huisdierenverzekeringen.nl/info/hulphond-verzekeren',
  'https://www.huisdierenverzekeringen.nl/info/wachttijd-dierenverzekering',
  'https://www.huisdierenverzekeringen.nl/info/gebitsbehandeling-hond-kat',
  'https://www.huisdierenverzekeringen.nl/info/chippen-kat',
  'https://www.huisdierenverzekeringen.nl/info/dierenverzekering-verstandig',
  'https://www.huisdierenverzekeringen.nl/info/wat-kost-een-hondenverzekering',
  'https://www.huisdierenverzekeringen.nl/info/onderzoek-kosten-huisdier',
  'https://www.huisdierenverzekeringen.nl/info/voeding-hond',
  'https://www.huisdierenverzekeringen.nl/info/crematie-hond-kat',
  'https://www.huisdierenverzekeringen.nl/info/castratie-sterilisatie-huisdier',
  'https://www.huisdierenverzekeringen.nl/info/ontwormen-hond',
  'https://www.huisdierenverzekeringen.nl/info/dierenspeciaalzaken-nederland',
  'https://www.huisdierenverzekeringen.nl/info/onderzoek-kosten-dierenarts-2023',
  'https://www.huisdierenverzekeringen.nl/info/hond-kat-vermist',
  'https://www.huisdierenverzekeringen.nl/info/hondenbelasting-2024',
  'https://www.huisdierenverzekeringen.nl/info/aansprakelijkheid-wa',
  'https://www.huisdierenverzekeringen.nl/info/kosten-consult-dierenarts',
  'https://www.huisdierenverzekeringen.nl/info/ontwormen-kat',
  'https://www.huisdierenverzekeringen.nl/info/dierenarts-nacht-weekend',
  'https://www.huisdierenverzekeringen.nl/info/kosten-rontgenfoto-hond-kat',
  'https://www.huisdierenverzekeringen.nl/info/hond-kat-pijnstillers',
  'https://www.huisdierenverzekeringen.nl/info/voeding-katten',
  'https://www.huisdierenverzekeringen.nl/info/bloemen-planten-katten',
  'https://www.huisdierenverzekeringen.nl/info/hondenbelasting-2023',
  'https://www.huisdierenverzekeringen.nl/info/dierenverzekering-belgie'
]

// Function to generate slug from URL
function generateSlug(url) {
  const path = url.replace('https://www.huisdierenverzekeringen.nl/info/', '')
  return path || 'index'
}

// Function to extract title from content
function extractTitle(content) {
  const titleMatch = content.match(/^# (.+)$/m)
  return titleMatch ? titleMatch[1] : 'Untitled'
}

// Function to extract meta description (if available in content)
function extractMetaDescription(content) {
  // Look for the first paragraph after the title
  const lines = content.split('\n')
  let foundTitle = false
  for (const line of lines) {
    if (line.startsWith('# ') && !foundTitle) {
      foundTitle = true
      continue
    }
    if (foundTitle && line.trim() && !line.startsWith('#') && !line.startsWith('![') && !line.startsWith('[')) {
      return line.trim()
    }
  }
  return ''
}

// Export for use in manual processing
module.exports = {
  urls,
  generateSlug,
  extractTitle,
  extractMetaDescription
}

console.log(`Total URLs to process: ${urls.length}`)
console.log('URLs:', urls)
