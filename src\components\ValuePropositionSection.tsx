import Image from "next/image";
import Link from "next/link";

export default function ValuePropositionSection() {
  return (
    <section className="hidden sm:block py-16 sm:py-20 lg:py-24 bg-tea_green-900">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-12">
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-celadon-100 mb-6">
            Waarom ZoekDierenverzekering.nl de beste keuze is voor huisdier verzekeren
          </h2>
          <p className="text-lg text-celadon-300 max-w-3xl mx-auto">
            Als onafhankelijk vergelijkingsplatform helpen wij je de beste dierenverzekering te vinden 
            die perfect past bij jouw huisdier en budget.
          </p>
        </div>

        {/* Benefits Grid */}
        <div className="grid md:grid-cols-3 gap-8 lg:gap-12">
          {/* Benefit 1 */}
          <div className="text-center group">
            <div className="w-20 h-20 bg-gradient-to-br from-celeste-400 to-celeste-300 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
              <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <h3 className="text-xl font-bold text-celadon-100 mb-4">
              100% Onafhankelijk Vergelijken
            </h3>
            <p className="text-celadon-300 leading-relaxed">
              Als onafhankelijk vergelijkingsplatform tonen we alle Nederlandse dierenverzekeraars 
              zonder verborgen kosten. Vergelijk premies, dekking en voorwaarden van OHRA, PetSecur, 
              Figo en andere top verzekeraars.
            </p>
          </div>

          {/* Benefit 2 */}
          <div className="text-center group">
            <div className="w-20 h-20 bg-gradient-to-br from-celadon-400 to-celadon-300 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
              <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
              </svg>
            </div>
            <h3 className="text-xl font-bold text-celadon-100 mb-4">
              Goedkoopste Dierenverzekering Vinden
            </h3>
            <p className="text-gray-600 leading-relaxed">
              Bespaar tot 30% door slim vergelijken. Onze algoritme vindt automatisch de beste 
              prijs-kwaliteitverhouding voor jouw specifieke situatie. Kattenverzekering vanaf €10, 
              hondenverzekering vanaf €15 per maand.
            </p>
          </div>

          {/* Benefit 3 */}
          <div className="text-center group">
            <div className="w-20 h-20 bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
              <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8h2a2 2 0 012 2v6a2 2 0 01-2 2h-2v4l-4-4H9a1.994 1.994 0 01-1.414-.586m0 0L11 14h4a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2v4l.586-.586z" />
              </svg>
            </div>
            <h3 className="text-xl font-bold text-[#2F2E51] mb-4">
              Expert Advies & Persoonlijke Service
            </h3>
            <p className="text-gray-600 leading-relaxed">
              Gratis advies van gecertificeerde verzekeringsexperts. We helpen je de juiste dekking 
              te kiezen die past bij jouw huisdier en budget. Nederlandse klantenservice, geen 
              verkooppraatjes.
            </p>
          </div>
        </div>

        {/* Pet Selection Cards */}
        <div className="mt-16 pt-16 border-t border-gray-200">
          <h3 className="text-xl sm:text-2xl lg:text-3xl font-bold text-center text-[#2F2E51] mb-8 px-2 leading-tight">
            Welke dierenverzekering past bij jouw huisdier?
          </h3>
          
          <div className="grid md:grid-cols-2 gap-4 sm:gap-6 md:gap-8 max-w-4xl mx-auto">
            {/* Dog Card */}
            <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-2xl p-4 sm:p-6 md:p-8 border border-blue-100 hover:border-blue-300 transition-all duration-300 group">
              <div className="flex flex-col sm:flex-row items-start sm:items-center mb-4 sm:mb-6">
                <div className="w-12 h-12 sm:w-16 sm:h-16 bg-blue-100 rounded-full flex items-center justify-center mb-3 sm:mb-0 sm:mr-4 flex-shrink-0">
                  <Image
                    src="/images/dog.webp"
                    alt="Hondenverzekering vergelijken"
                    width={48}
                    height={48}
                    className="w-8 h-8 sm:w-12 sm:h-12 object-cover rounded-full"
                  />
                </div>
                <h4 className="text-lg sm:text-xl md:text-2xl font-bold text-[#2F2E51] leading-tight">Hondenverzekering Vergelijken</h4>
              </div>
              <p className="text-sm sm:text-base text-gray-600 mb-4 sm:mb-6 leading-relaxed">
                Vergelijk hondenverzekeringen van alle Nederlandse aanbieders. Van kleine rassen 
                tot grote honden - vind de beste dekking voor jouw trouwe viervoeter.
              </p>
              <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-3 sm:gap-4">
                <div className="text-lg sm:text-xl md:text-2xl font-bold text-blue-600">Vanaf €17/maand</div>
                <Link 
                  href="/hondenverzekering" 
                  className="bg-[#2F2E51] text-white px-4 py-2 sm:px-6 sm:py-3 rounded-full text-sm sm:text-base font-semibold hover:bg-[#2F2E51]/90 transition-colors group-hover:scale-105 transform duration-300 w-full sm:w-auto text-center"
                >
                  <span className="sm:hidden">Bekijk Honden</span><span className="hidden sm:inline">Hondenverzekering Vergelijken</span>
                </Link>
              </div>
            </div>

            {/* Cat Card */}
            <div className="bg-gradient-to-br from-pink-50 to-rose-50 rounded-2xl p-4 sm:p-6 md:p-8 border border-pink-100 hover:border-pink-300 transition-all duration-300 group">
              <div className="flex flex-col sm:flex-row items-start sm:items-center mb-4 sm:mb-6">
                <div className="w-12 h-12 sm:w-16 sm:h-16 bg-pink-100 rounded-full flex items-center justify-center mb-3 sm:mb-0 sm:mr-4 flex-shrink-0">
                  <img 
                    src="/images/cat.webp" 
                    alt="Kattenverzekering vergelijken" 
                    className="w-8 h-8 sm:w-12 sm:h-12 object-cover rounded-full"
                    width="48"
                    height="48"
                  />
                </div>
                <h4 className="text-lg sm:text-xl md:text-2xl font-bold text-[#2F2E51] leading-tight">Kattenverzekering Vergelijken</h4>
              </div>
              <p className="text-sm sm:text-base text-gray-600 mb-4 sm:mb-6 leading-relaxed">
                Kies uit de beste kattenverzekeringen in Nederland. Van kitten tot senior kat - 
                bescherm je huiskat tegen hoge dierenarts kosten.
              </p>
              <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-3 sm:gap-4">
                <div className="text-lg sm:text-xl md:text-2xl font-bold text-pink-600">Vanaf €11/maand</div>
                <Link 
                  href="/kattenverzekering" 
                  className="bg-[#2F2E51] text-white px-4 py-2 sm:px-6 sm:py-3 rounded-full text-sm sm:text-base font-semibold hover:bg-[#2F2E51]/90 transition-colors group-hover:scale-105 transform duration-300 w-full sm:w-auto text-center"
                >
                  <span className="sm:hidden">Bekijk Katten</span><span className="hidden sm:inline">Kattenverzekering Vergelijken</span>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}