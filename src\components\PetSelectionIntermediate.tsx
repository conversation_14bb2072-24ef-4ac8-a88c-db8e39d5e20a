import Link from "next/link";

export default function PetSelectionIntermediate() {
  return (
    <section className="relative py-8 sm:py-12 lg:py-16 z-20 bg-white">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-center">
          {/* Pet selection card centered vertically between hero and best beoordeeld sections */}
          <div className="w-full max-w-2xl lg:max-w-3xl">
            <div className="bg-white/95 backdrop-blur-sm p-4 sm:p-6 lg:p-8 rounded-2xl shadow-xl border border-white/20 ring-1 ring-[#2F2E51]/5">
              <h3 className="text-lg sm:text-xl lg:text-2xl font-bold text-[#2F2E51] mb-4 sm:mb-6 text-center">
                Welke dierenverzekering past bij jouw huisdier?
              </h3>
              
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 lg:gap-6">
                {/* Dog card */}
                <div className="group relative overflow-hidden rounded-xl border-2 border-[#2F2E51]/20 hover:border-[#2F2E51] transition-all duration-300 shadow-md hover:shadow-lg">
                  <Link href="/hondenverzekering" className="block text-[#2F2E51] font-bold hover:bg-gradient-to-br hover:from-blue-50/80 hover:to-indigo-50/80 transition-all duration-300">
                    <div className="flex sm:flex-col lg:flex-row items-center sm:items-stretch">
                      <div className="flex-shrink-0 sm:w-full lg:w-24 relative overflow-hidden">
                        <img 
                          src="/images/dog.webp" 
                          alt="Hondenverzekering vergelijken - beste verzekering voor honden vanaf €15 per maand" 
                          title="Hondenverzekering vergelijken Nederland"
                          className="w-24 h-20 sm:w-full sm:h-24 lg:w-24 lg:h-20 object-cover group-hover:scale-110 transition-transform duration-300"
                          width="163"
                          height="109"
                          loading="eager"
                        />
                        <div className="absolute inset-0 bg-gradient-to-t from-[#2F2E51]/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                      </div>
                      <div className="flex-1 sm:flex-none ml-4 sm:ml-0 lg:ml-4">
                        <div className="h-full flex items-center justify-center sm:justify-center lg:justify-start py-3 sm:py-4">
                          <div className="text-lg lg:text-xl font-bold group-hover:text-[#2F2E51] transition-colors duration-300 flex items-center gap-2">
                            Hond
                            <svg className="w-5 h-5 transform group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                            </svg>
                          </div>
                        </div>
                      </div>
                    </div>
                  </Link>
                </div>
                
                {/* Cat card */}
                <div className="group relative overflow-hidden rounded-xl border-2 border-[#2F2E51]/20 hover:border-[#2F2E51] transition-all duration-300 shadow-md hover:shadow-lg">
                  <Link href="/kattenverzekering" className="block text-[#2F2E51] font-bold hover:bg-gradient-to-br hover:from-pink-50/80 hover:to-rose-50/80 transition-all duration-300">
                    <div className="flex sm:flex-col lg:flex-row items-center sm:items-stretch">
                      <div className="flex-shrink-0 sm:w-full lg:w-24 relative overflow-hidden">
                        <img 
                          src="/images/cat.webp" 
                          alt="Kattenverzekering vergelijken - beste verzekering voor katten vanaf €10 per maand" 
                          title="Kattenverzekering vergelijken Nederland"
                          className="w-24 h-20 sm:w-full sm:h-24 lg:w-24 lg:h-20 object-cover group-hover:scale-110 transition-transform duration-300"
                          width="163"
                          height="109"
                          loading="eager"
                        />
                        <div className="absolute inset-0 bg-gradient-to-t from-[#2F2E51]/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                      </div>
                      <div className="flex-1 sm:flex-none ml-4 sm:ml-0 lg:ml-4">
                        <div className="h-full flex items-center justify-center sm:justify-center lg:justify-start py-3 sm:py-4">
                          <div className="text-lg lg:text-xl font-bold group-hover:text-[#2F2E51] transition-colors duration-300 flex items-center gap-2">
                            Kat
                            <svg className="w-5 h-5 transform group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                            </svg>
                          </div>
                        </div>
                      </div>
                    </div>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}