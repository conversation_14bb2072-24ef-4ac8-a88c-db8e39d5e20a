"use client"

import Image from 'next/image'
import { motion } from 'framer-motion'

export default function BekenVanSection() {
  const mediaLogos = [
    { name: '<PERSON>', src: '/images/hart-van-nederland-56586a.png', alt: '<PERSON> logo' },
    { name: '<PERSON><PERSON><PERSON><PERSON>d', src: '/images/algemeen-dagblad-56586a.png', alt: 'Algemeen Dagblad logo' },
    { name: '<PERSON><PERSON>', src: '/images/een-vandaag-56586a.png', alt: '<PERSON><PERSON> logo' },
    { name: '<PERSON> Telegraaf', src: '/images/telegraaf-56586a.png', alt: 'De Telegraaf logo' },
    { name: 'Nu.nl', src: '/images/nu-nl-56586a.png', alt: 'Nu.nl logo' },
    { name: '<PERSON><PERSON><PERSON><PERSON>', src: '/images/margriet-56586a.png', alt: '<PERSON>grie<PERSON> logo' }
  ]

  return (
    <section className="w-full bg-white py-16">
      <div className="max-w-[1284px] mx-auto px-4">
        <motion.h2
          className="text-[42px] font-bold text-[#2F2E51] text-center leading-[1.2] mb-16 font-figtree"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
          viewport={{ once: true, margin: "-100px" }}
        >
          Bekend van
        </motion.h2>

        <div className="flex justify-center items-center gap-8 flex-wrap">
          {mediaLogos.map((logo, index) => (
            <motion.div
              key={index}
              className="w-[154px] h-[100px] opacity-75 hover:opacity-100 transition-opacity duration-200 flex items-center justify-center"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 0.75, y: 0 }}
              whileHover={{ opacity: 1, scale: 1.05 }}
              transition={{ duration: 0.5, delay: 0.4 + (index * 0.1), ease: "easeOut" }}
              viewport={{ once: true, margin: "-50px" }}
            >
              <Image
                src={logo.src}
                alt={logo.alt}
                width={154}
                height={100}
                className="max-w-full max-h-full object-contain"
              />
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  )
}