# Perfect Figma to Code Implementation Prompt

Use this exact prompt structure to get pixel-perfect implementations from Figma designs:

```
Use figma mcp https://www.figma.com/design/[FILE-ID]/[FILE-NAME]?node-id=[NODE-ID]&t=[TOKEN] 

Check it and re-create an exact copy of [SECTION/COMPONENT NAME] container.

Make sure to do the same for the other sections below that of figma.
```

## Key Success Factors:

### 1. **Provide Full Figma URL - SPECIFIC NODE**
- ✅ **BEST**: Selected element URL with specific node-id
  ```
  https://www.figma.com/design/SJuPMRRg8VGy0yzhD1xLNR/Verzekering?node-id=25-1003&t=HrUdi1OJ22uh0E7q-0
  ```
- ❌ **AVOID**: Root/page URL with generic node-id
  ```
  https://www.figma.com/design/SJuPMRRg8VGy0yzhD1xLNR/Verzekering?node-id=25-1002&p=f&t=HrUdi1OJ22uh0E7q-0
  ```

**Why specific node URLs work better:**
- Points directly to the exact element/section
- Gives <PERSON> precise context about what to implement
- Reduces analysis time and improves accuracy

### 2. **Be Specific About Section**
- Name the exact section/container you want copied
- Example: "Onze meest gelezen artikelen over huisdierenverzekeringen container"

### 3. **Request Complete Implementation**
- Ask for "other sections below" to get full page implementation
- This triggers systematic analysis of entire layout

### 4. **Let Claude Use MCP Tools**
- Don't provide descriptions - let Claude analyze the design directly
- Claude will automatically download images and extract exact measurements

## What This Prompt Achieves:

✅ **Pixel-perfect measurements** from Figma data  
✅ **Real image downloads** with proper cropping  
✅ **Exact typography** (font families, sizes, weights)  
✅ **Precise colors** (hex codes from design)  
✅ **Correct spacing** (margins, padding, gaps)  
✅ **Proper layout structure** (grid systems, flex layouts)  
✅ **Component organization** (clean, reusable React components)  
✅ **Complete implementation** (all sections in correct order)  

## How to Get the Right URL:

### ✅ **CORRECT Method:**
1. In Figma, **select the specific element/section** you want to implement
2. Right-click → **"Copy link to selection"** 
3. This gives you a URL like: `node-id=25-1003` (specific element)

### ❌ **INCORRECT Method:**
1. Just copying the page URL without selecting anything
2. This gives you a URL like: `node-id=25-1002&p=f` (page root)

## Example Usage:

```
Use figma mcp https://www.figma.com/design/SJuPMRRg8VGy0yzhD1xLNR/Verzekering?node-id=25-1003&t=HrUdi1OJ22uh0E7q-0 

Check it and re-create an exact copy of the hero section container.

Make sure to do the same for the other sections below that of figma.
```

**Key Difference:**
- `node-id=25-1003` = Specific selected element ✅
- `node-id=25-1002&p=f` = Page root (too broad) ❌

## Pro Tips:

1. **Always provide the full Figma URL** - not just file/node IDs
2. **Be specific about which section** you want to start with
3. **Request "other sections below"** for complete implementation
4. **Let Claude handle the technical analysis** - don't pre-describe the design
5. **Trust the MCP tools** - they provide exact measurements and assets

This approach leverages Claude's Figma MCP integration to deliver production-ready code that matches designs exactly.