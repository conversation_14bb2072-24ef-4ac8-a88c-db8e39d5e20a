import type { Metadata } from "next";
import { Poppins, Figtree } from "next/font/google";
import "./globals.css";
import ErrorBoundary from "@/components/ErrorBoundary";
import PerformanceMonitor from "@/components/PerformanceMonitor";
import SkipNavigation from "@/components/SkipNavigation";

const poppins = Poppins({
  variable: "--font-poppins",
  subsets: ["latin"],
  weight: ["300", "400", "500", "600", "700", "800"],
});

const figtree = Figtree({
  variable: "--font-figtree",
  subsets: ["latin"],
  weight: ["300", "400", "500", "600", "700", "800"],
});

export const metadata: Metadata = {
  title: "ZoekDierenverzekering.nl - Vergelijk Alle Nederlandse Dierenverzekeringen",
  description: "Vergelijk 7+ dierenverzekeringen Nederland ✓ Beste honden- & kattenverzekering vanaf €10/maand ✓ Onafhankelijk ✓ 30% korting ✓ Direct afsluiten",
  keywords: [
    "dierenverzekering",
    "dierenverzekering vergelijken",
    "huisdierenverzekering",
    "hondenverzekering",
    "kattenverzekering",
    "goedkoopste dierenverzekering",
    "beste dierenverzekering",
    "Nederlandse dierenverzekeraars",
    "onafhankelijk vergelijken"
  ],
  authors: [{ name: "ZoekDierenverzekering.nl" }],
  creator: "ZoekDierenverzekering.nl",
  publisher: "ZoekDierenverzekering.nl",
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: "your-google-verification-code",
  },
  alternates: {
    canonical: "https://zoekdierenverzekering.nl",
    languages: {
      'nl-NL': 'https://zoekdierenverzekering.nl',
    },
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="nl-NL" suppressHydrationWarning>
      <head>
        {/* Enhanced Schema Markup */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "LocalBusiness",
              "name": "ZoekDierenverzekering.nl",
              "description": "Onafhankelijk vergelijken van dierenverzekeringen in Nederland. Vind de beste en goedkoopste verzekering voor hond en kat.",
              "url": "https://zoekdierenverzekering.nl",
              "logo": "https://zoekdierenverzekering.nl/images/logo.png",
              "image": "https://zoekdierenverzekering.nl/images/hero.webp",
              "telephone": "+31-20-1234567",
              "address": {
                "@type": "PostalAddress",
                "addressCountry": "NL",
                "addressLocality": "Amsterdam",
                "addressRegion": "Noord-Holland"
              },
              "geo": {
                "@type": "GeoCoordinates",
                "latitude": "52.3676",
                "longitude": "4.9041"
              },
              "areaServed": {
                "@type": "Country",
                "name": "Netherlands",
                "sameAs": "https://en.wikipedia.org/wiki/Netherlands"
              },
              "serviceType": "Insurance Comparison Service",
              "priceRange": "€10-€50 per month",
              "aggregateRating": {
                "@type": "AggregateRating",
                "ratingValue": "4.8",
                "reviewCount": "1247",
                "bestRating": "5",
                "worstRating": "1"
              },
              "sameAs": [
                "https://www.facebook.com/zoekdierenverzekering",
                "https://www.linkedin.com/company/zoekdierenverzekering"
              ]
            })
          }}
        />
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "WebSite",
              "name": "ZoekDierenverzekering.nl",
              "url": "https://zoekdierenverzekering.nl",
              "potentialAction": {
                "@type": "SearchAction",
                "target": "https://zoekdierenverzekering.nl/vergelijken?q={search_term_string}",
                "query-input": "required name=search_term_string"
              }
            })
          }}
        />
        {/* Preconnect for performance */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        
        {/* Language and regional targeting */}
        <link rel="alternate" hrefLang="nl-NL" href="https://zoekdierenverzekering.nl" />
        <meta name="geo.region" content="NL" />
        <meta name="geo.placename" content="Netherlands" />
        <meta name="language" content="Dutch" />
        
        {/* Viewport configuration for mobile */}
        <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
        
        {/* Enhanced SEO meta tags */}
        <meta name="theme-color" content="#114125" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="apple-mobile-web-app-title" content="ZoekDierenverzekering" />
      </head>
      <body
        className={`${poppins.variable} ${figtree.variable} font-sans antialiased`}
      >
        <ErrorBoundary>
          <SkipNavigation />
          <PerformanceMonitor />
          <main id="main-content">
            {children}
          </main>
        </ErrorBoundary>
      </body>
    </html>
  );
}
