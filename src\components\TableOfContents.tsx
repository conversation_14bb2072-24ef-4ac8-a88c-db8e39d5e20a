'use client'

import { useEffect } from 'react'

interface TableOfContentsProps {
  items: Array<{ id: string; title: string; level: number }>
}

export default function TableOfContents({ items }: TableOfContentsProps) {
  useEffect(() => {
    // Add smooth scrolling behavior to all anchor links
    const handleClick = (e: Event) => {
      const target = e.target as HTMLAnchorElement
      if (target.tagName === 'A' && target.href.includes('#')) {
        e.preventDefault()
        const targetId = target.href.split('#')[1]
        const targetElement = document.getElementById(targetId)
        
        if (targetElement) {
          // Calculate offset for fixed navigation
          const navHeight = 100 // Approximate navigation height
          const elementPosition = targetElement.getBoundingClientRect().top + window.pageYOffset
          const offsetPosition = elementPosition - navHeight

          window.scrollTo({
            top: offsetPosition,
            behavior: 'smooth'
          })
          
          // Update URL without causing a page jump
          window.history.pushState(null, '', `#${targetId}`)
        }
      }
    }

    // Add event listeners to TOC links
    const tocLinks = document.querySelectorAll('.toc-link')
    tocLinks.forEach(link => {
      link.addEventListener('click', handleClick)
    })

    // Cleanup event listeners
    return () => {
      tocLinks.forEach(link => {
        link.removeEventListener('click', handleClick)
      })
    }
  }, [])

  if (!items || items.length === 0) {
    return null
  }

  return (
    <div className="bg-gray-50 rounded-xl p-6 mb-12">
      <h3 className="text-lg font-bold text-[#2F2E51] mb-4">Inhoudsopgave</h3>
      <ul className="space-y-2">
        {items
          .filter(item => item.level <= 3) // Only show h1, h2, h3
          .map((item, index) => (
            <li key={index} className={item.level === 1 ? 'font-semibold' : item.level === 3 ? 'ml-4 text-sm' : ''}>
              <a
                href={`#${item.id}`}
                className="toc-link text-blue-600 hover:text-blue-800 transition-colors"
              >
                {index + 1}. {item.title}
              </a>
            </li>
          ))}
      </ul>
    </div>
  )
}