"use client"

import Image from "next/image";

export default function ValuePropositionMobile() {
  return (
    <section className="block sm:hidden bg-white">
      {/* Hero Image Section - Node 27:56 */}
      <div className="w-full px-4">
        <div className="relative max-w-[295px] aspect-[295/374] mx-auto rounded-[10px] overflow-hidden">
          <Image
            src="/images/hero.webp"
            alt="Huisdierenverzekering"
            width={295}
            height={374}
            className="w-full h-full object-cover"
          />
        </div>
      </div>

      {/* Container Section - Node 27:57 */}
      <div className="w-full max-w-[390px] mx-auto px-4 py-4 bg-white">
        {/* Title */}
        <h2 className="text-[24px] font-bold text-[#2F2E51] leading-[28.8px] mb-6">
          Daarom kies je voor een{'\n'}
          dierenverzekering
        </h2>
        
        {/* Description */}
        <p className="text-[16px] font-normal text-[#2F2E51] leading-[25.6px] mb-8">
          <PERSON><PERSON> huisdier brengt zorgkosten met zich mee.{'\n'}
          Iedere hond of kat moet wel eens naar de dokter,{'\n'}
          net als jijzelf. Voorkom financiële verrassingen bij{'\n'}
          de dierenarts en sluit een passende{'\n'}
          dierenverzekering af voor je trouwe dierenvriend.
        </p>

        {/* List Section */}
        <div className="space-y-0">
          {/* Item 1 */}
          <div className="flex items-center h-[36px] mb-0">
            <div className="w-[18px] h-[18px] mr-[9.5px] mt-2 flex-shrink-0">
              <svg width="18" height="18" viewBox="0 0 18 18" fill="none">
                <path 
                  d="M15 4.5L6.75 12.75L3 9" 
                  stroke="#2F2E51" 
                  strokeWidth="2" 
                  strokeLinecap="round" 
                  strokeLinejoin="round"
                />
              </svg>
            </div>
            <span className="text-[16px] font-normal text-[#2F2E51] leading-[36px]">
              Vergelijk álle verzekeraars
            </span>
          </div>

          {/* Item 2 */}
          <div className="flex items-center h-[36px] mb-0">
            <div className="w-[18px] h-[18px] mr-[9.5px] mt-2 flex-shrink-0">
              <svg width="18" height="18" viewBox="0 0 18 18" fill="none">
                <path 
                  d="M15 4.5L6.75 12.75L3 9" 
                  stroke="#2F2E51" 
                  strokeWidth="2" 
                  strokeLinecap="round" 
                  strokeLinejoin="round"
                />
              </svg>
            </div>
            <span className="text-[16px] font-normal text-[#2F2E51] leading-[36px]">
              Alle kleine lettertjes op een rij
            </span>
          </div>

          {/* Item 3 */}
          <div className="flex items-center h-[36px]">
            <div className="w-[18px] h-[18px] mr-[9.5px] mt-2 flex-shrink-0">
              <svg width="18" height="18" viewBox="0 0 18 18" fill="none">
                <path 
                  d="M15 4.5L6.75 12.75L3 9" 
                  stroke="#2F2E51" 
                  strokeWidth="2" 
                  strokeLinecap="round" 
                  strokeLinejoin="round"
                />
              </svg>
            </div>
            <span className="text-[16px] font-normal text-[#2F2E51] leading-[36px]">
              Reviews van andere huisdiereigenaren
            </span>
          </div>
        </div>
      </div>

      {/* Slim vergelijken Section - Node 27:76 */}
      <div className="w-full max-w-[390px] mx-auto px-4 py-4 bg-white">
        <h3 className="text-[24px] font-bold text-[#2F2E51] leading-[28.8px] mb-4">
          Slim vergelijken op{'\n'}
          Huisdierenverzekeringen.nl
        </h3>
        <p className="text-[16px] font-normal text-[#2F2E51] leading-[25.6px]">
          Bekijk, vergelijk en kies in een paar klikken de{'\n'}
          perfecte verzekering voor jouw viervoeter.
        </p>
      </div>

      {/* Actuele en correcte informatie Section - Node 27:79 */}
      <div className="w-[360px] h-[572.25px] mx-auto bg-white border-b border-[#E5E5E5]">
        {/* Image */}
        <div className="w-full h-[240.11px] rounded-[10px] overflow-hidden mb-0">
          <Image
            src="/images/man-about-to-kiss-a-cute-puppy-dog-outdoors-2025-03-07-23-33-25-utc.webp"
            alt="Vrouw en hond"
            width={360}
            height={240}
            className="w-full h-full object-cover"
          />
        </div>

        {/* Content */}
        <div className="px-[15px] pt-0 pb-4">
          <h4 className="text-[24px] font-bold text-[#2F2E51] leading-[28.8px] mb-6">
            Actuele en correcte informatie
          </h4>
          <p className="text-[16px] font-normal text-[#2F2E51] leading-[25.6px]">
            Je huisdier is een belangrijk onderdeel van je{'\n'}
            gezin. Dat gezinslid wil je graag de best mogelijke{'\n'}
            zorg kunnen geven. Wij weten dat als geen ander{'\n'}
            en daarom helpen we je graag. Met informatie{'\n'}
            waarop je kunt vertrouwen. Samen met onze{'\n'}
            experts zorgen we ervoor dat alle{'\n'}
            verzekeringsvoorwaarden, premies en andere{'\n'}
            gegevens actueel zijn. En je vindt bij ons de juiste{'\n'}
            tips en adviezen.
          </p>
        </div>
      </div>

      {/* Onafhankelijk vergelijken Section - Node 27:84 */}
      <div className="w-[360px] h-[621.84px] mx-auto bg-white border-b border-[#E5E5E5]">
        {/* Image */}
        <div className="w-full h-[240.11px] rounded-[10px] overflow-hidden mt-6 mb-0">
          <Image
            src="/images/cat.webp"
            alt="Vrouw en kat"
            width={360}
            height={240}
            className="w-full h-full object-cover"
          />
        </div>

        {/* Content */}
        <div className="px-[15px] pt-0 pb-4">
          <h4 className="text-[24px] font-bold text-[#2F2E51] leading-[28.8px] mb-6">
            Onafhankelijk vergelijken
          </h4>
          <p className="text-[16px] font-normal text-[#2F2E51] leading-[25.6px]">
            Huisdierenverzekeringen.nl is een onafhankelijk{'\n'}
            informatie- en vergelijkingsplatform. Ons team{'\n'}
            van experts houdt alle informatie op de website{'\n'}
            accuraat en actueel. In onze overzichtelijke{'\n'}
            vergelijker kan je vervolgens makkelijk{'\n'}
            huisdierenverzekeringen bekijken, vergelijken en{'\n'}
            jouw beste keuze maken. Of je nu een hond, kat,{'\n'}
            konijn of papegaai wilt verzekeren, bij ons vind je{'\n'}
            álle huisdierenverzekeringen. Er is altijd een{'\n'}
            verzekering die goed past bij jou en jouw huisdier.
          </p>
        </div>
      </div>

      {/* Huisdieren in Nederland Section - Node 27:89 */}
      <div className="w-[360px] min-h-[394.39px] mx-auto bg-[#FFF5ED] mt-6">
        <div className="px-6 py-6">
          <h4 className="text-[24px] font-bold text-[#2F2E51] leading-[28.8px] mb-4">
            Huisdieren in Nederland
          </h4>
          <p className="text-[16px] font-normal text-[#2F2E51] leading-[25.6px] mb-6">
            Wist je dat…
          </p>

          {/* List Section */}
          <div className="space-y-0">
            {/* Item 1 */}
            <div className="flex items-start h-[72px] mb-0">
              <div className="w-[18px] h-[18px] mr-[9.5px] mt-2 flex-shrink-0">
                <svg width="18" height="18" viewBox="0 0 18 18" fill="none">
                  <path 
                    d="M15 4.5L6.75 12.75L3 9" 
                    stroke="#2F2E51" 
                    strokeWidth="2" 
                    strokeLinecap="round" 
                    strokeLinejoin="round"
                  />
                </svg>
              </div>
              <span className="text-[16px] font-normal text-[#2F2E51] leading-[36px]">
                …Nederland ongeveer 1,9 miljoen{'\n'}
                honden en ruim 3 miljoen katten telt?
              </span>
            </div>

            {/* Item 2 */}
            <div className="flex items-start h-[72px] mb-0">
              <div className="w-[18px] h-[18px] mr-[9.5px] mt-2 flex-shrink-0">
                <svg width="18" height="18" viewBox="0 0 18 18" fill="none">
                  <path 
                    d="M15 4.5L6.75 12.75L3 9" 
                    stroke="#2F2E51" 
                    strokeWidth="2" 
                    strokeLinecap="round" 
                    strokeLinejoin="round"
                  />
                </svg>
              </div>
              <span className="text-[16px] font-normal text-[#2F2E51] leading-[36px]">
                …naar schatting één op de twintig{'\n'}
                huisdieren in ons land verzekerd is?
              </span>
            </div>

            {/* Item 3 */}
            <div className="flex items-start h-[108px]">
              <div className="w-[18px] h-[18px] mr-[9.5px] mt-2 flex-shrink-0">
                <svg width="18" height="18" viewBox="0 0 18 18" fill="none">
                  <path 
                    d="M15 4.5L6.75 12.75L3 9" 
                    stroke="#2F2E51" 
                    strokeWidth="2" 
                    strokeLinecap="round" 
                    strokeLinejoin="round"
                  />
                </svg>
              </div>
              <span className="text-[16px] font-normal text-[#2F2E51] leading-[36px]">
                …het verzekeren van een huisdier al{'\n'}
                mogelijk is vanaf zo&apos;n 15 euro per{'\n'}
                maand?
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Testimonials Section - Node 27:108 */}
      <div className="w-[360px] h-[500.7px] mx-auto bg-[#FFF5ED]">
        <div className="px-6 py-6">
          <h4 className="text-[24px] font-bold text-[#2F2E51] leading-[28.8px] mb-4">
            Wat zeggen anderen?
          </h4>

          <div className="space-y-6">
            {/* Testimonial 1 */}
            <p className="text-[16px] font-normal text-[#2F2E51] leading-[25.6px]">
              &ldquo;Na een ongelukje moesten we met onze{'\n'}
              hond Bram naar de dierenarts, met een{'\n'}
              gepeperde rekening als gevolg. Gelukkig{'\n'}
              hadden we via Huisdierenverzekeringen.nl{'\n'}
              een passende verzekering gevonden. Ik had{'\n'}
              er niet aan moeten denken als we die{'\n'}
              rekening helemaal zelf hadden moeten{'\n'}
              betalen...&rdquo; – Marjan
            </p>

            {/* Testimonial 2 */}
            <p className="text-[16px] font-normal text-[#2F2E51] leading-[25.6px]">
              &ldquo;Onze hond is inmiddels zo&apos;n vier jaar{'\n'}
              verzekerd. Gelukkig hebben we de{'\n'}
              verzekering nog niet hoeven gebruiken{'\n'}
              voor een grote ingreep. Het geeft mij{'\n'}
              echter een fijn en gerust gevoel dat hij{'\n'}
              goed verzekerd is, op het moment dat dit{'\n'}
              onverhoopt wél nodig is.&rdquo; – W. de Jongh
            </p>
          </div>
        </div>
      </div>
    </section>
  );
}